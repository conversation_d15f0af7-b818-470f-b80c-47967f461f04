document.addEventListener('DOMContentLoaded', function() {
    const trackingForm = document.getElementById('tracking-form');
    const trackingResults = document.getElementById('tracking-results');

    if (trackingForm) {
        trackingForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Show loading spinner
            trackingResults.innerHTML = '<div class="loading-spinner"></div>';

            const formData = new FormData(trackingForm);
            const trackingNumber = formData.get('tracking_number');

            // Make actual API call to fetch tracking data
            fetch(trackingForm.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data) {
                    displayTrackingResults(data.data);
                } else {
                    // Show error message
                    trackingResults.innerHTML = `
                        <div class="tracking-error">
                            ${data.message || 'Shipment not found. Please check the tracking number and try again.'}
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error fetching tracking data:', error);
                trackingResults.innerHTML = `
                    <div class="tracking-error">
                        An error occurred while fetching tracking data. Please try again later.
                    </div>
                `;
            });
        });
    }

    function displayTrackingResults(data) {
        if (!data || data.error) {
            trackingResults.innerHTML = `
                <div class="tracking-error">
                    ${data?.error || 'Shipment not found. Please check the tracking number and try again.'}
                </div>
            `;
            return;
        }

        // Format the shipment status for display
        const statusClass = getStatusClass(data.status);

        // Build the HTML for the tracking results
        let html = `
            <div class="result-container">
                <!-- Tracking Header with Status -->
                <div class="tracking-header-section">
                    <div class="tracking-header-left">
                        <h2 class="tracking-title">Tracking Details</h2>
                        <div class="tracking-number">
                            <span class="label">Tracking Number:</span>
                            <span class="value">${data.tracking_number}</span>
                        </div>
                        <div class="tracking-barcode">
                            <svg id="barcode"></svg>
                        </div>
                    </div>
                    <div class="tracking-header-right">
                        <div class="order-status ${statusClass.replace('status-', '')}">
                            <i class="fas fa-circle status-icon"></i>
                            ${formatStatus(data.status)}
                        </div>
                        <div class="status-details">
                            <div class="status-message">${getStatusMessage(data.status)}</div>
                        </div>
                        <div class="tracking-date">
                            <span class="label">Last Updated:</span>
                            <span class="value">${data.updated_at || 'N/A'}</span>
                        </div>
                    </div>
                </div>

                <!-- Simple Tracking Details Section -->
                <div class="tracking-details-section">
                    <div class="section-header">
                        <i class="fas fa-list section-icon"></i>
                        <h3 class="section-title">Tracking Details</h3>
                    </div>
                    <div class="tracking-details-list">
                        ${(() => {
                            const events = data.events || [];
                            const currentStatus = data.status.toLowerCase();

                            if (events.length === 0) {
                                return '<div class="no-tracking-details">No tracking details available yet.</div>';
                            }

                            // Sort events by date (oldest first for chronological flow)
                            const sortedEvents = events.sort((a, b) => {
                                const dateA = new Date(a.date + ' ' + a.time);
                                const dateB = new Date(b.date + ' ' + b.time);
                                return dateA - dateB;
                            });

                            return sortedEvents.map((event, index) => {
                                const isCurrentStatus = event.status.toLowerCase() === currentStatus;
                                const statusColor = getStatusColor(event.status);
                                const statusIcon = getIconForStatus(event.status);

                                return `
                                    <div class="tracking-detail-item ${isCurrentStatus ? 'current-status' : ''}">
                                        <div class="detail-icon" style="background-color: ${statusColor}">
                                            <i class="fas ${statusIcon}"></i>
                                        </div>
                                        <div class="detail-content">
                                            <div class="detail-header">
                                                <div class="detail-status" style="color: ${statusColor}">
                                                    ${formatStatus(event.status)}
                                                    ${isCurrentStatus ? '<span class="current-badge">Current</span>' : ''}
                                                </div>
                                                <div class="detail-date">${event.date} ${event.time}</div>
                                            </div>
                                            <div class="detail-location">
                                                <i class="fas fa-map-marker-alt"></i>
                                                ${event.location || 'Location not specified'}
                                            </div>
                                            ${event.message ? `<div class="detail-message">${event.message}</div>` : ''}
                                            ${event.remarks ? `<div class="detail-remarks"><strong>Remarks:</strong> ${event.remarks}</div>` : ''}
                                        </div>
                                    </div>
                                `;
                            }).join('');
                        })()}
                    </div>
                </div>

                <!-- Shipment Progress Summary -->
                <div class="progress-summary-section">
                    <div class="progress-card">
                        <div class="progress-icon">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="progress-info">
                            <div class="progress-label">Origin</div>
                            <div class="progress-value">${data.origin || 'N/A'}</div>
                        </div>
                    </div>
                    <div class="progress-arrow">
                        <i class="fas fa-long-arrow-alt-right"></i>
                    </div>
                    <div class="progress-card">
                        <div class="progress-icon">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="progress-info">
                            <div class="progress-label">In Transit</div>
                            <div class="progress-value">${data.status.toLowerCase().includes('transit') ? 'Active' : 'Completed'}</div>
                        </div>
                    </div>
                    <div class="progress-arrow">
                        <i class="fas fa-long-arrow-alt-right"></i>
                    </div>
                    <div class="progress-card">
                        <div class="progress-icon">
                            <i class="fas fa-flag-checkered"></i>
                        </div>
                        <div class="progress-info">
                            <div class="progress-label">Destination</div>
                            <div class="progress-value">${data.destination || 'N/A'}</div>
                        </div>
                    </div>
                </div>

                <!-- Shipment Information Grid - 3 columns layout -->
                <div class="shipment-info-grid">
                    <!-- Column 1: Shipper Information -->
                    <div class="info-section">
                        <div class="section-header">
                            <i class="fas fa-user section-icon"></i>
                            <h3 class="section-title">Shipper Information</h3>
                        </div>
                        <div class="section-content">
                            <div class="info-row">
                                <div class="info-label">Name:</div>
                                <div class="info-value">${data.shipper_name || 'N/A'}</div>
                            </div>
                            <div class="section-divider"></div>
                            <div class="info-row">
                                <div class="info-label">Address:</div>
                                <div class="info-value">${formatAddress(data.shipper_address) || 'N/A'}</div>
                            </div>
                            <div class="section-divider"></div>
                            <div class="info-row">
                                <div class="info-label">Phone:</div>
                                <div class="info-value">${data.shipper_phone || 'N/A'}</div>
                            </div>
                            <div class="section-divider"></div>
                            <div class="info-row">
                                <div class="info-label">Email:</div>
                                <div class="info-value">${data.shipper_email || 'N/A'}</div>
                            </div>
                        </div>
                    </div>

                    <!-- Column 2: Receiver Information -->
                    <div class="info-section">
                        <div class="section-header">
                            <i class="fas fa-user section-icon"></i>
                            <h3 class="section-title">Receiver Information</h3>
                        </div>
                        <div class="section-content">
                            <div class="info-row">
                                <div class="info-label">Name:</div>
                                <div class="info-value">${data.receiver_name || 'N/A'}</div>
                            </div>
                            <div class="section-divider"></div>
                            <div class="info-row">
                                <div class="info-label">Address:</div>
                                <div class="info-value">${formatAddress(data.receiver_address) || 'N/A'}</div>
                            </div>
                            <div class="section-divider"></div>
                            <div class="info-row">
                                <div class="info-label">Phone:</div>
                                <div class="info-value">${data.receiver_phone || 'N/A'}</div>
                            </div>
                            <div class="section-divider"></div>
                            <div class="info-row">
                                <div class="info-label">Email:</div>
                                <div class="info-value">${data.receiver_email || 'N/A'}</div>
                            </div>
                        </div>
                    </div>

                    <!-- Column 3: Shipment Details -->
                    <div class="info-section">
                        <div class="section-header">
                            <i class="fas fa-shipping-fast section-icon"></i>
                            <h3 class="section-title">Shipment Details</h3>
                        </div>
                        <div class="section-content">
                            <div class="info-row">
                                <div class="info-label">Service Type:</div>
                                <div class="info-value">${data.service_type || 'Standard'}</div>
                            </div>
                            <div class="section-divider"></div>
                            <div class="info-row">
                                <div class="info-label">Carrier:</div>
                                <div class="info-value">${data.carrier_name || data.carrier || 'N/A'}</div>
                            </div>
                            <div class="section-divider"></div>
                            <div class="info-row">
                                <div class="info-label">Carrier Reference:</div>
                                <div class="info-value">${data.carrier_reference || 'N/A'}</div>
                            </div>
                            <div class="section-divider"></div>
                            <div class="info-row">
                                <div class="info-label">Payment Method:</div>
                                <div class="info-value">${data.payment_method || data.payment_mode || 'N/A'}</div>
                            </div>
                            <div class="section-divider"></div>
                            <div class="info-row">
                                <div class="info-label">Weight:</div>
                                <div class="info-value">${data.weight || 'N/A'} kg</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Full-width info cards -->
                <div class="info-cards-container">
                    <!-- Shipping Details Card -->
                    <div class="info-card-horizontal">
                        <div class="info-card-header">
                            <i class="fas fa-dollar-sign section-icon"></i>
                            <h3 class="section-title">Shipping Details</h3>
                        </div>
                        <div class="info-card-content">
                            <div class="info-details-grid">
                                <div class="info-detail-item">
                                    <div class="detail-label">Shipping Cost:</div>
                                    <div class="detail-value">${data.shipping_cost ? '$' + data.shipping_cost : 'N/A'}</div>
                                </div>
                                <div class="info-detail-item">
                                    <div class="detail-label">Current Location:</div>
                                    <div class="detail-value">${data.current_location_name || getCurrentLocation(data)}</div>
                                </div>
                                <div class="info-detail-item">
                                    <div class="detail-label">Shipping Method:</div>
                                    <div class="detail-value">${data.shipping_mode || data.shipment_mode || 'Standard Shipping'}</div>
                                </div>
                                <div class="info-detail-item">
                                    <div class="detail-label">Carrier:</div>
                                    <div class="detail-value">${data.carrier || 'N/A'}</div>
                                </div>
                                <div class="info-detail-item">
                                    <div class="detail-label">Payment Method:</div>
                                    <div class="detail-value">${data.payment_mode || 'N/A'}</div>
                                </div>
                                <div class="info-detail-item">
                                    <div class="detail-label">Service Type:</div>
                                    <div class="detail-value">${data.service_type || data.shipment_type || 'Standard'}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Important Dates Card -->
                    <div class="info-card-horizontal">
                        <div class="info-card-header">
                            <i class="fas fa-calendar section-icon"></i>
                            <h3 class="section-title">Important Dates</h3>
                        </div>
                        <div class="info-card-content">
                            <div class="info-details-grid">
                                <div class="info-detail-item">
                                    <div class="detail-label">Pickup Date:</div>
                                    <div class="detail-value">${formatDate(data.pickup_date) || data.pickup_date_formatted || 'N/A'}</div>
                                </div>
                                <div class="info-detail-item">
                                    <div class="detail-label">Departure Date:</div>
                                    <div class="detail-value">${formatDate(data.departure_date) || data.departure_date_formatted || 'N/A'}</div>
                                </div>
                                <div class="info-detail-item">
                                    <div class="detail-label">Estimated Delivery:</div>
                                    <div class="detail-value">${formatDate(data.expected_delivery_date) || data.estimated_delivery || 'N/A'}</div>
                                </div>
                                <div class="info-detail-item">
                                    <div class="detail-label">Order Date:</div>
                                    <div class="detail-value">${formatDate(data.order_date) || 'N/A'}</div>
                                </div>
                                <div class="info-detail-item">
                                    <div class="detail-label">Processing Date:</div>
                                    <div class="detail-value">${formatDate(data.processing_date) || 'N/A'}</div>
                                </div>
                                <div class="info-detail-item">
                                    <div class="detail-label">Last Updated:</div>
                                    <div class="detail-value">${data.events && data.events.length > 0 ? data.events[0].date + ' ' + data.events[0].time : 'N/A'}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Package Information Card -->
                    <div class="info-card-horizontal">
                        <div class="info-card-header">
                            <i class="fas fa-box section-icon"></i>
                            <h3 class="section-title">Package Information</h3>
                        </div>
                        <div class="info-card-content">
                            ${generatePackageInfoHorizontal(data)}
                        </div>
                    </div>
                </div>



                <!-- Map Container -->
                <div class="map-section">
                    <div class="section-header">
                        <i class="fas fa-map-marked-alt section-icon"></i>
                        <h3 class="section-title">Shipment Location</h3>
                    </div>
                    <div class="map-container">
                        <div id="shipment-map"></div>
                    </div>
                </div>

                <!-- Item Information Card -->
                <div class="info-card-horizontal">
                    <div class="info-card-header">
                        <i class="fas fa-box section-icon"></i>
                        <h3 class="section-title">Item Information</h3>
                    </div>
                    <div class="info-card-content">
                        ${generateItemInfoHorizontal(data)}
                    </div>
                </div>

                <!-- Document Management Section (Hidden by default, shown only when requests exist) -->
                <div class="document-management-section" id="document-management-section" style="display: none;">
                    <div class="section-header">
                        <i class="fas fa-file-alt section-icon"></i>
                        <h3 class="section-title">Document Management</h3>
                    </div>
                    <div class="document-content">
                        <div id="document-requests-container">
                            <div class="loading-documents">
                                <i class="fas fa-spinner fa-spin"></i>
                                <span>Loading document requests...</span>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        `;

        trackingResults.innerHTML = html;

        // Load document requests for this shipment
        loadDocumentRequests(data.tracking_number);

        // Load notifications for this shipment
        loadNotifications(data.tracking_number);

        // Update track details panel
        updateTrackDetailsPanel(data);

        // Initialize map
        setTimeout(() => {
            try {
                const map = L.map('shipment-map').setView([0, 0], 2);

                // Use a grayscale tile layer for black and white theme
                L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
                    subdomains: 'abcd',
                    maxZoom: 19
                }).addTo(map);

                // Get coordinates based on origin and destination
                // Use geocoding to get more accurate coordinates
                let originCoords, destinationCoords;

                // Try to extract country information from origin and destination
                const originCountry = extractCountry(data.origin);
                const destinationCountry = extractCountry(data.destination);

                // Use predefined coordinates for common countries
                // This is a simple approach - in a production app, you would use a geocoding API
                originCoords = getCountryCoordinates(originCountry) || [0, 0];
                destinationCoords = getCountryCoordinates(destinationCountry) || [0, 0];

                // If we couldn't determine coordinates, use defaults
                if (originCoords[0] === 0 && originCoords[1] === 0) {
                    originCoords = [-7.2575, 112.7521]; // Default: Surabaya
                }

                if (destinationCoords[0] === 0 && destinationCoords[1] === 0) {
                    destinationCoords = [-7.9797, 112.6304]; // Default: Malang
                }

                // Calculate current location based on status and events
                let currentLocationCoords;
                let progress = 0.5; // Default to halfway

                // Try to determine progress based on events
                if (data.events && data.events.length > 0) {
                    const totalEvents = data.events.length;
                    const completedEvents = data.events.filter(e =>
                        e.status.toLowerCase() !== 'pending' &&
                        e.status.toLowerCase() !== 'delivered'
                    ).length;

                    if (totalEvents > 1) {
                        progress = Math.min(completedEvents / (totalEvents - 1), 1);
                    }
                }

                const status = data.status.toLowerCase();
                if (status.includes('delivered') || status.includes('completed')) {
                    currentLocationCoords = destinationCoords;
                    progress = 1;
                } else if (status.includes('transit')) {
                    // Calculate a point between origin and destination based on estimated progress
                    currentLocationCoords = [
                        originCoords[0] + (destinationCoords[0] - originCoords[0]) * progress,
                        originCoords[1] + (destinationCoords[1] - originCoords[1]) * progress
                    ];
                } else {
                    currentLocationCoords = originCoords;
                    progress = 0;
                }

                // Add markers for origin, current location, and destination with custom icons
                const originIcon = L.divIcon({
                    className: 'origin-marker',
                    html: '<div class="marker-icon origin-icon"><i class="fas fa-warehouse"></i></div>',
                    iconSize: [40, 40],
                    iconAnchor: [20, 20]
                });

                const destinationIcon = L.divIcon({
                    className: 'destination-marker',
                    html: '<div class="marker-icon destination-icon"><i class="fas fa-flag-checkered"></i></div>',
                    iconSize: [40, 40],
                    iconAnchor: [20, 20]
                });

                const truckIcon = L.divIcon({
                    className: 'current-location-marker',
                    html: '<div class="marker-icon truck-icon"><i class="fas fa-truck"></i></div>',
                    iconSize: [50, 50],
                    iconAnchor: [25, 25]
                });

                const originMarker = L.marker(originCoords, {icon: originIcon}).addTo(map)
                    .bindPopup(`<b>Origin:</b> ${data.origin || 'Unknown'}`);

                const currentMarker = L.marker(currentLocationCoords, {icon: truckIcon}).addTo(map)
                    .bindPopup(`<b>Current Location:</b> ${getCurrentLocation(data)}`);

                const destinationMarker = L.marker(destinationCoords, {icon: destinationIcon}).addTo(map)
                    .bindPopup(`<b>Destination:</b> ${data.destination || 'Unknown'}`);

                // Draw a line between origin and destination (remaining route)
                const routeLine = L.polyline([originCoords, destinationCoords], {
                    color: '#888888',
                    weight: 5,
                    opacity: 0.8,
                    dashArray: '10, 10'
                }).addTo(map);

                // Draw a solid line for the completed portion of the journey
                if (progress > 0) {
                    const completedRoute = L.polyline([originCoords, currentLocationCoords], {
                        color: '#4C51BF',
                        weight: 6,
                        opacity: 1
                    }).addTo(map);
                }

                // Fit the map to show all markers with good padding
                const bounds = L.latLngBounds([originCoords, destinationCoords]);
                map.fitBounds(bounds, { padding: [100, 100] });

                // Initialize tab functionality
                const tabBtns = document.querySelectorAll('.tab-btn');
                const tabPanes = document.querySelectorAll('.tab-pane');

                tabBtns.forEach(btn => {
                    btn.addEventListener('click', function() {
                        // Remove active class from all buttons and panes
                        tabBtns.forEach(b => b.classList.remove('active'));
                        tabPanes.forEach(p => p.classList.remove('active'));

                        // Add active class to clicked button and corresponding pane
                        this.classList.add('active');
                        const tabId = this.getAttribute('data-tab');
                        document.getElementById(tabId).classList.add('active');
                    });
                });
            } catch (e) {
                console.error('Error initializing map:', e);
            }
        }, 100);

        // Initialize the timeline animation and barcode
        setTimeout(() => {
            try {
                // Generate barcode
                if (typeof JsBarcode !== 'undefined') {
                    JsBarcode("#barcode", data.tracking_number, {
                        format: "CODE128",
                        lineColor: "#000",
                        width: 1.5,
                        height: 40,
                        displayValue: false
                    });
                }

                // Initialize timeline animation
                const timelineEvents = document.querySelectorAll('.timeline-event');
                timelineEvents.forEach((event, index) => {
                    setTimeout(() => {
                        event.classList.add('animate-in');
                        const dot = event.querySelector('.event-dot');
                        if (dot) dot.classList.add('animate-pulse');
                    }, 300 * index);
                });

                // Initialize status chart
                const chartContainer = document.getElementById('shipment-status-chart');
                if (chartContainer) {
                    // Sample data - in production this would come from the server
                    const statusData = [
                        { month: 'Jan', count: 45, color: '#4C51BF' },
                        { month: 'Feb', count: 38, color: '#4C51BF' },
                        { month: 'Mar', count: 52, color: '#4C51BF' },
                        { month: 'Apr', count: 65, color: '#4C51BF' },
                        { month: 'May', count: 48, color: '#4C51BF' },
                        { month: 'Jun', count: 40, color: '#4C51BF' },
                        { month: 'Jul', count: 55, color: '#4C51BF' },
                        { month: 'Aug', count: 60, color: '#4C51BF' }
                    ];

                    // Find the maximum value for scaling
                    const maxValue = Math.max(...statusData.map(item => item.count));

                    // Create the chart HTML
                    let chartHTML = '<div class="status-chart">';

                    // Add bars
                    statusData.forEach(item => {
                        const heightPercentage = (item.count / maxValue) * 100;
                        chartHTML += `
                            <div class="chart-column">
                                <div class="chart-bar-container">
                                    <div class="chart-bar" style="height: ${heightPercentage}%; background-color: ${item.color};">
                                        <span class="bar-value">${item.count}</span>
                                    </div>
                                </div>
                                <div class="chart-label">${item.month}</div>
                            </div>
                        `;
                    });

                    chartHTML += '</div>';
                    chartContainer.innerHTML = chartHTML;

                    // Animate the bars
                    const bars = chartContainer.querySelectorAll('.chart-bar');
                    bars.forEach((bar, index) => {
                        setTimeout(() => {
                            bar.style.opacity = '1';
                            bar.style.transform = 'scaleY(1)';
                        }, 100 * index);
                    });
                }

                // Initialize delivery route map
                const mapContainer = document.getElementById('delivery-route-map');
                if (mapContainer) {
                    mapContainer.innerHTML = `
                        <div class="route-map-placeholder">
                            <div class="map-point origin">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>Origin</span>
                            </div>
                            <div class="map-route-line"></div>
                            <div class="map-point current">
                                <i class="fas fa-truck"></i>
                                <span>Current</span>
                            </div>
                            <div class="map-route-line"></div>
                            <div class="map-point destination">
                                <i class="fas fa-flag-checkered"></i>
                                <span>Destination</span>
                            </div>
                        </div>
                    `;
                }
            } catch (e) {
                console.error('Error initializing visualizations:', e);
            }
        }, 300);
    }

    function getStatusClass(status) {
        status = status.toLowerCase();
        if (status.includes('delivered')) return 'status-delivered';
        if (status.includes('transit')) return 'status-in-transit';
        if (status.includes('pending') || status.includes('processing')) return 'status-pending';
        if (status.includes('delayed') || status.includes('failed')) return 'status-delayed';
        if (status.includes('customs') || status.includes('clearance')) return 'status-customs';
        return '';
    }

    function getIconForStatus(status) {
        status = status.toLowerCase();
        if (status.includes('delivered')) return 'fa-check-circle';
        if (status.includes('out_for_delivery')) return 'fa-shipping-fast';
        if (status.includes('transit')) return 'fa-truck';
        if (status.includes('processing')) return 'fa-cog';
        if (status.includes('pending')) return 'fa-clock';
        if (status.includes('cancelled')) return 'fa-times-circle';
        if (status.includes('delayed')) return 'fa-exclamation-triangle';
        if (status.includes('on_hold')) return 'fa-pause-circle';
        if (status.includes('customs') || status.includes('clearance')) return 'fa-clipboard-check';
        if (status.includes('arrival') || status.includes('arrived')) return 'fa-warehouse';
        if (status.includes('pickup')) return 'fa-hand-holding-box';
        if (status.includes('return')) return 'fa-undo';
        return 'fa-info-circle'; // Default icon
    }

    function getStatusColor(status) {
        const statusLower = status.toLowerCase();

        if (statusLower.includes('delivered')) {
            return '#4CAF50'; // Green
        } else if (statusLower.includes('transit')) {
            return '#2196F3'; // Blue
        } else if (statusLower.includes('processing')) {
            return '#FF9800'; // Orange
        } else if (statusLower.includes('pending') || statusLower.includes('created')) {
            return '#9E9E9E'; // Grey
        } else if (statusLower.includes('customs') || statusLower.includes('clearance')) {
            return '#795548'; // Brown
        } else if (statusLower.includes('delayed') || statusLower.includes('exception')) {
            return '#F44336'; // Red
        } else if (statusLower.includes('arrival') || statusLower.includes('arrived')) {
            return '#607D8B'; // Blue Grey
        } else if (statusLower.includes('departed') || statusLower.includes('departure')) {
            return '#3F51B5'; // Indigo
        } else if (statusLower.includes('out_for_delivery')) {
            return '#9C27B0'; // Purple
        } else {
            return '#607D8B'; // Default Blue Grey
        }
    }

    function formatStatus(status) {
        // Convert snake_case or kebab-case to Title Case
        return status
            .replace(/_/g, ' ')
            .replace(/-/g, ' ')
            .replace(/\\b\\w/g, l => l.toUpperCase());
    }

    function getEventOrder(status) {
        status = status.toLowerCase();
        if (status.includes('delivered')) return 5;
        if (status.includes('out_for_delivery')) return 4;
        if (status.includes('arrived')) return 3;
        if (status.includes('transit')) return 2;
        if (status.includes('processing')) return 1;
        return 0;
    }

    function getStatusMessage(status) {
        status = status.toLowerCase();
        if (status.includes('delivered')) {
            return 'Your package has been delivered successfully.';
        } else if (status.includes('out_for_delivery')) {
            return 'Your package is out for delivery and will arrive today.';
        } else if (status.includes('arrived')) {
            return 'Your package has arrived at the local facility.';
        } else if (status.includes('transit')) {
            return 'Your package is in transit to the destination.';
        } else if (status.includes('processing')) {
            return 'Your package is being processed at the origin facility.';
        } else if (status.includes('pending')) {
            return 'Your shipment has been shipped and is pending pickup.';
        } else {
            return 'Tracking information is being updated.';
        }
    }

    function getCurrentLocation(data) {
        // Try to get the current location from the most recent event
        if (data.events && data.events.length > 0) {
            const latestEvent = data.events.sort((a, b) => {
                return new Date(b.date + ' ' + b.time) - new Date(a.date + ' ' + a.time);
            })[0];

            if (latestEvent.location) {
                return latestEvent.location;
            }
        }

        // Fallback based on status
        const status = data.status.toLowerCase();
        if (status.includes('delivered')) {
            return data.destination || 'Destination';
        } else if (status.includes('out_for_delivery')) {
            return data.destination + ' Local Facility' || 'Local Delivery Facility';
        } else if (status.includes('arrived')) {
            return data.destination + ' Distribution Center' || 'Distribution Center';
        } else if (status.includes('transit')) {
            return 'In Transit';
        } else {
            return data.origin || 'Origin Facility';
        }
    }

    function formatAddress(address) {
        if (!address) return 'N/A';

        // Format the address to make it more readable
        // Replace commas with line breaks for display
        return address.replace(/,/g, ', ');
    }

    function formatDate(dateStr) {
        if (!dateStr) return 'N/A';

        // Check if the date includes time information (like 2025-04-22 00:00:00)
        if (dateStr.includes(' ')) {
            // Extract just the date part
            dateStr = dateStr.split(' ')[0];
        }

        // Try to parse the date
        try {
            const date = new Date(dateStr);
            if (isNaN(date.getTime())) return dateStr; // Return original if invalid

            // Format as Month Day, Year (e.g., April 22, 2025)
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        } catch (e) {
            return dateStr; // Return original if parsing fails
        }
    }

    // Function to generate HTML for package information
    function generatePackageInfo(data, useListLayout = false) {
        // If no packages, show default package info
        if (!data.packages || data.packages.length === 0) {
            if (useListLayout) {
                return `
                    <div class="list-layout">
                        <div class="info-row">
                            <div class="info-label">Package Type:</div>
                            <div class="info-value">${data.package_type || 'Standard'}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">Dimensions:</div>
                            <div class="info-value">${data.dimensions || 'N/A'}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">Weight:</div>
                            <div class="info-value">${data.weight ? data.weight + ' kg' : 'N/A'}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">Items:</div>
                            <div class="info-value">${data.items || 'N/A'}</div>
                        </div>
                    </div>
                `;
            } else {
                return `
                    <div class="info-row">
                        <div class="info-label">Package Type:</div>
                        <div class="info-value">${data.package_type || 'Standard'}</div>
                    </div>
                    <div class="section-divider"></div>
                    <div class="info-row">
                        <div class="info-label">Dimensions:</div>
                        <div class="info-value">${data.dimensions || 'N/A'}</div>
                    </div>
                    <div class="section-divider"></div>
                    <div class="info-row">
                        <div class="info-label">Weight:</div>
                        <div class="info-value">${data.weight ? data.weight + ' kg' : 'N/A'}</div>
                    </div>
                    <div class="section-divider"></div>
                    <div class="info-row">
                        <div class="info-label">Items:</div>
                        <div class="info-value">${data.items || 'N/A'}</div>
                    </div>
                `;
            }
        }

        // If we have packages, display each one
        let html = '';

        if (useListLayout) {
            html += '<div class="list-layout">';
        }

        data.packages.forEach((pkg, index) => {
            if (useListLayout) {
                if (index > 0) {
                    html += `<div class="package-number">Package ${index + 1}</div>`;
                }

                html += `
                    <div class="info-row">
                        <div class="info-label">Package Type:</div>
                        <div class="info-value">${pkg.piece_type || data.package_type || 'Standard'}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Dimensions:</div>
                        <div class="info-value">${pkg.length && pkg.width && pkg.height ?
                            pkg.length + ' x ' + pkg.width + ' x ' + pkg.height + ' cm' :
                            data.dimensions || 'N/A'}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Weight:</div>
                        <div class="info-value">${pkg.weight ? pkg.weight + ' kg' : (data.weight ? data.weight + ' kg' : 'N/A')}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Description:</div>
                        <div class="info-value">${pkg.description || data.items || 'N/A'}</div>
                    </div>
                `;
            } else {
                if (index > 0) {
                    html += `<div class="package-divider"></div>`;
                }

                html += `
                    <div class="package-container">
                        ${index > 0 ? `<div class="package-number">Package ${index + 1}</div>` : ''}
                        <div class="info-row">
                            <div class="info-label">Package Type:</div>
                            <div class="info-value">${pkg.piece_type || data.package_type || 'Standard'}</div>
                        </div>
                        <div class="section-divider"></div>
                        <div class="info-row">
                            <div class="info-label">Dimensions:</div>
                            <div class="info-value">${pkg.length && pkg.width && pkg.height ?
                                pkg.length + ' x ' + pkg.width + ' x ' + pkg.height + ' cm' :
                                data.dimensions || 'N/A'}</div>
                        </div>
                        <div class="section-divider"></div>
                        <div class="info-row">
                            <div class="info-label">Weight:</div>
                            <div class="info-value">${pkg.weight ? pkg.weight + ' kg' : (data.weight ? data.weight + ' kg' : 'N/A')}</div>
                        </div>
                        <div class="section-divider"></div>
                        <div class="info-row">
                            <div class="info-label">Description:</div>
                            <div class="info-value">${pkg.description || data.items || 'N/A'}</div>
                        </div>
                    </div>
                `;
            }
        });

        if (useListLayout) {
            html += '</div>';
        }

        return html;
    }

    // Function to generate HTML for package information in horizontal layout
    function generatePackageInfoHorizontal(data) {
        // If no packages, show default package info
        if (!data.packages || data.packages.length === 0) {
            return `
                <div class="package-info-grid">
                    <div class="info-detail-item">
                        <div class="detail-label">Package Type:</div>
                        <div class="detail-value">${data.package_type || 'Standard'}</div>
                    </div>
                    <div class="info-detail-item">
                        <div class="detail-label">Dimensions:</div>
                        <div class="detail-value">${data.dimensions || 'N/A'}</div>
                    </div>
                    <div class="info-detail-item">
                        <div class="detail-label">Weight:</div>
                        <div class="detail-value">${data.weight ? data.weight + ' kg' : 'N/A'}</div>
                    </div>
                    <div class="info-detail-item">
                        <div class="detail-label">Items:</div>
                        <div class="detail-value">${data.items || 'N/A'}</div>
                    </div>
                    <div class="info-detail-item">
                        <div class="detail-label">Volume Weight:</div>
                        <div class="detail-value">${data.volume_weight ? data.volume_weight + ' kg' : 'N/A'}</div>
                    </div>
                    <div class="info-detail-item">
                        <div class="detail-label">Chargeable Weight:</div>
                        <div class="detail-value">${data.chargeable_weight ? data.chargeable_weight + ' kg' : 'N/A'}</div>
                    </div>
                    <div class="info-detail-item">
                        <div class="detail-label">Packaging:</div>
                        <div class="detail-value">${data.packaging || 'Standard Packaging'}</div>
                    </div>
                    <div class="info-detail-item">
                        <div class="detail-label">Fragile:</div>
                        <div class="detail-value">${data.is_fragile ? 'Yes' : 'No'}</div>
                    </div>
                </div>
            `;
        }

        // If we have packages, display each one
        let html = '<div class="package-info-grid">';

        data.packages.forEach((pkg, index) => {
            if (index > 0) {
                html += `<div class="package-separator">Package ${index + 1}</div>`;
            }

            html += `
                <div class="info-detail-item">
                    <div class="detail-label">Package Type:</div>
                    <div class="detail-value">${pkg.piece_type || data.package_type || 'Standard'}</div>
                </div>
                <div class="info-detail-item">
                    <div class="detail-label">Dimensions:</div>
                    <div class="detail-value">${pkg.length && pkg.width && pkg.height ?
                        pkg.length + ' x ' + pkg.width + ' x ' + pkg.height + ' cm' :
                        data.dimensions || 'N/A'}</div>
                </div>
                <div class="info-detail-item">
                    <div class="detail-label">Weight:</div>
                    <div class="detail-value">${pkg.weight ? pkg.weight + ' kg' : (data.weight ? data.weight + ' kg' : 'N/A')}</div>
                </div>
                <div class="info-detail-item">
                    <div class="detail-label">Description:</div>
                    <div class="detail-value">${pkg.description || data.items || 'N/A'}</div>
                </div>
            `;
        });

        html += '</div>';
        return html;
    }

    // Function to generate HTML for item information
    function generateItemInfo(data, useListLayout = false) {
        // If no packages, show default item info
        if (!data.packages || data.packages.length === 0) {
            if (useListLayout) {
                return `
                    <div class="list-layout">
                        <div class="info-row">
                            <div class="info-label">Item Name:</div>
                            <div class="info-value">${data.package_type || 'Package'}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">Description:</div>
                            <div class="info-value">${data.items || 'No description available'}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">Quantity:</div>
                            <div class="info-value">${data.quantity || '1'}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">Weight:</div>
                            <div class="info-value">${data.weight ? data.weight + ' kg' : 'Not specified'}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">Dimensions:</div>
                            <div class="info-value">${data.dimensions || 'Not specified'}</div>
                        </div>
                    </div>
                `;
            } else {
                return `
                    <div class="info-row">
                        <div class="info-label">Item Name:</div>
                        <div class="info-value">${data.package_type || 'Package'}</div>
                    </div>
                    <div class="section-divider"></div>
                    <div class="info-row">
                        <div class="info-label">Description:</div>
                        <div class="info-value">${data.items || 'No description available'}</div>
                    </div>
                    <div class="section-divider"></div>
                    <div class="info-row">
                        <div class="info-label">Quantity:</div>
                        <div class="info-value">${data.quantity || '1'}</div>
                    </div>
                    <div class="section-divider"></div>
                    <div class="info-row">
                        <div class="info-label">Weight:</div>
                        <div class="info-value">${data.weight ? data.weight + ' kg' : 'Not specified'}</div>
                    </div>
                    <div class="section-divider"></div>
                    <div class="info-row">
                        <div class="info-label">Dimensions:</div>
                        <div class="info-value">${data.dimensions || 'Not specified'}</div>
                    </div>
                `;
            }
        }

        // If we have packages, display items from each package
        let html = '';

        if (useListLayout) {
            html += '<div class="list-layout">';
        }

        data.packages.forEach((pkg, index) => {
            if (useListLayout) {
                if (index > 0) {
                    html += `<div class="package-number">Items in Package ${index + 1}</div>`;
                }

                html += `
                    <div class="info-row">
                        <div class="info-label">Item Name:</div>
                        <div class="info-value">${pkg.piece_type || data.package_type || 'Package'}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Description:</div>
                        <div class="info-value">${pkg.description || data.items || 'No description available'}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Quantity:</div>
                        <div class="info-value">${pkg.quantity || data.quantity || '1'}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Weight:</div>
                        <div class="info-value">${pkg.weight ? pkg.weight + ' kg' : (data.weight ? data.weight + ' kg' : 'Not specified')}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Dimensions:</div>
                        <div class="info-value">${pkg.length && pkg.width && pkg.height ?
                            pkg.length + ' x ' + pkg.width + ' x ' + pkg.height + ' cm' :
                            data.dimensions || 'Not specified'}</div>
                    </div>
                `;
            } else {
                if (index > 0) {
                    html += `<div class="package-divider"></div>`;
                }

                html += `
                    <div class="item-container">
                        ${index > 0 ? `<div class="package-number">Items in Package ${index + 1}</div>` : ''}
                        <div class="info-row">
                            <div class="info-label">Item Name:</div>
                            <div class="info-value">${pkg.piece_type || data.package_type || 'Package'}</div>
                        </div>
                        <div class="section-divider"></div>
                        <div class="info-row">
                            <div class="info-label">Description:</div>
                            <div class="info-value">${pkg.description || data.items || 'No description available'}</div>
                        </div>
                        <div class="section-divider"></div>
                        <div class="info-row">
                            <div class="info-label">Quantity:</div>
                            <div class="info-value">${pkg.quantity || data.quantity || '1'}</div>
                        </div>
                        <div class="section-divider"></div>
                        <div class="info-row">
                            <div class="info-label">Weight:</div>
                            <div class="info-value">${pkg.weight ? pkg.weight + ' kg' : (data.weight ? data.weight + ' kg' : 'Not specified')}</div>
                        </div>
                        <div class="section-divider"></div>
                        <div class="info-row">
                            <div class="info-label">Dimensions:</div>
                            <div class="info-value">${pkg.length && pkg.width && pkg.height ?
                                pkg.length + ' x ' + pkg.width + ' x ' + pkg.height + ' cm' :
                                data.dimensions || 'Not specified'}</div>
                        </div>
                    </div>
                `;
            }
        });

        if (useListLayout) {
            html += '</div>';
        }

        return html;
    }

    // Function to generate HTML for item information in horizontal layout
    function generateItemInfoHorizontal(data) {
        // If no packages, show default item info
        if (!data.packages || data.packages.length === 0) {
            return `
                <div class="package-info-grid">
                    <div class="info-detail-item">
                        <div class="detail-label">Item Name:</div>
                        <div class="detail-value">${data.package_type || 'Package'}</div>
                    </div>
                    <div class="info-detail-item">
                        <div class="detail-label">Description:</div>
                        <div class="detail-value">${data.items || 'No description available'}</div>
                    </div>
                    <div class="info-detail-item">
                        <div class="detail-label">Quantity:</div>
                        <div class="detail-value">${data.quantity || '1'}</div>
                    </div>
                    <div class="info-detail-item">
                        <div class="detail-label">Weight:</div>
                        <div class="detail-value">${data.weight ? data.weight + ' kg' : 'Not specified'}</div>
                    </div>
                    <div class="info-detail-item">
                        <div class="detail-label">Dimensions:</div>
                        <div class="detail-value">${data.dimensions || 'Not specified'}</div>
                    </div>
                </div>
            `;
        }

        // If we have packages, display items from each package
        let html = '<div class="package-info-grid">';

        data.packages.forEach((pkg, index) => {
            if (index > 0) {
                html += `<div class="package-separator">Items in Package ${index + 1}</div>`;
            }

            html += `
                <div class="info-detail-item">
                    <div class="detail-label">Item Name:</div>
                    <div class="detail-value">${pkg.piece_type || data.package_type || 'Package'}</div>
                </div>
                <div class="info-detail-item">
                    <div class="detail-label">Description:</div>
                    <div class="detail-value">${pkg.description || data.items || 'No description available'}</div>
                </div>
                <div class="info-detail-item">
                    <div class="detail-label">Quantity:</div>
                    <div class="detail-value">${pkg.quantity || data.quantity || '1'}</div>
                </div>
                <div class="info-detail-item">
                    <div class="detail-label">Weight:</div>
                    <div class="detail-value">${pkg.weight ? pkg.weight + ' kg' : (data.weight ? data.weight + ' kg' : 'Not specified')}</div>
                </div>
                <div class="info-detail-item">
                    <div class="detail-label">Dimensions:</div>
                    <div class="detail-value">${pkg.length && pkg.width && pkg.height ?
                        pkg.length + ' x ' + pkg.width + ' x ' + pkg.height + ' cm' :
                        data.dimensions || 'Not specified'}</div>
                </div>
            `;
        });

        html += '</div>';
        return html;
    }

    // Extract country name from an address string
    function extractCountry(addressStr) {
        if (!addressStr) return '';

        // Common country names to look for
        const countries = [
            'USA', 'United States', 'US', 'America',
            'UK', 'United Kingdom', 'England', 'Britain',
            'Canada', 'Australia', 'New Zealand',
            'China', 'Japan', 'India', 'Russia',
            'Germany', 'France', 'Italy', 'Spain',
            'Brazil', 'Mexico', 'Argentina',
            'South Africa', 'Nigeria', 'Egypt',
            'Indonesia', 'Malaysia', 'Singapore',
            'South Korea', 'North Korea',
            'Saudi Arabia', 'UAE', 'United Arab Emirates',
            'Netherlands', 'Belgium', 'Sweden', 'Norway',
            'Denmark', 'Finland', 'Iceland',
            'Portugal', 'Greece', 'Turkey',
            'Poland', 'Ukraine', 'Romania',
            'Thailand', 'Vietnam', 'Philippines',
            'Pakistan', 'Bangladesh', 'Sri Lanka',
            'Kenya', 'Ethiopia', 'Ghana',
            'Chile', 'Colombia', 'Peru',
            'Switzerland', 'Austria', 'Ireland',
            'New York', 'California', 'Texas', 'Florida',
            'London', 'Paris', 'Berlin', 'Rome', 'Madrid',
            'Tokyo', 'Beijing', 'Delhi', 'Mumbai',
            'Sydney', 'Melbourne', 'Toronto', 'Vancouver',
            'Dubai', 'Riyadh', 'Cairo',
            'Johannesburg', 'Lagos', 'Nairobi',
            'Singapore', 'Hong Kong', 'Seoul',
            'Moscow', 'St Petersburg',
            'Rio de Janeiro', 'Sao Paulo', 'Mexico City',
            'Los Angeles', 'Chicago', 'Houston',
            'Capetown', 'South Africa'
        ];

        // Try to find a country in the address string
        const words = addressStr.split(/[\s,]+/);
        for (const country of countries) {
            if (addressStr.toLowerCase().includes(country.toLowerCase())) {
                return country;
            }
        }

        // If no country found, return the last word as it might be a country/city
        return words[words.length - 1] || '';
    }

    // Get coordinates for common countries and cities
    function getCountryCoordinates(countryName) {
        if (!countryName) return null;

        const coordinatesMap = {
            // Countries
            'usa': [37.0902, -95.7129],
            'united states': [37.0902, -95.7129],
            'us': [37.0902, -95.7129],
            'america': [37.0902, -95.7129],
            'uk': [55.3781, -3.4360],
            'united kingdom': [55.3781, -3.4360],
            'england': [52.3555, -1.1743],
            'britain': [55.3781, -3.4360],
            'canada': [56.1304, -106.3468],
            'australia': [-25.2744, 133.7751],
            'new zealand': [-40.9006, 174.8860],
            'china': [35.8617, 104.1954],
            'japan': [36.2048, 138.2529],
            'india': [20.5937, 78.9629],
            'russia': [61.5240, 105.3188],
            'germany': [51.1657, 10.4515],
            'france': [46.2276, 2.2137],
            'italy': [41.8719, 12.5674],
            'spain': [40.4637, -3.7492],
            'brazil': [-14.2350, -51.9253],
            'mexico': [23.6345, -102.5528],
            'argentina': [-38.4161, -63.6167],
            'south africa': [-30.5595, 22.9375],
            'nigeria': [9.0820, 8.6753],
            'egypt': [26.8206, 30.8025],
            'indonesia': [-0.7893, 113.9213],
            'malaysia': [4.2105, 101.9758],
            'singapore': [1.3521, 103.8198],
            'south korea': [35.9078, 127.7669],
            'north korea': [40.3399, 127.5101],
            'saudi arabia': [23.8859, 45.0792],
            'uae': [23.4241, 53.8478],
            'united arab emirates': [23.4241, 53.8478],

            // Cities
            'new york': [40.7128, -74.0060],
            'los angeles': [34.0522, -118.2437],
            'chicago': [41.8781, -87.6298],
            'houston': [29.7604, -95.3698],
            'london': [51.5074, -0.1278],
            'paris': [48.8566, 2.3522],
            'berlin': [52.5200, 13.4050],
            'rome': [41.9028, 12.4964],
            'madrid': [40.4168, -3.7038],
            'tokyo': [35.6762, 139.6503],
            'beijing': [39.9042, 116.4074],
            'delhi': [28.7041, 77.1025],
            'mumbai': [19.0760, 72.8777],
            'sydney': [-33.8688, 151.2093],
            'melbourne': [-37.8136, 144.9631],
            'toronto': [43.6532, -79.3832],
            'vancouver': [49.2827, -123.1207],
            'dubai': [25.2048, 55.2708],
            'riyadh': [24.7136, 46.6753],
            'cairo': [30.0444, 31.2357],
            'johannesburg': [-26.2041, 28.0473],
            'lagos': [6.5244, 3.3792],
            'nairobi': [-1.2921, 36.8219],
            'hong kong': [22.3193, 114.1694],
            'seoul': [37.5665, 126.9780],
            'moscow': [55.7558, 37.6173],
            'st petersburg': [59.9343, 30.3351],
            'rio de janeiro': [-22.9068, -43.1729],
            'sao paulo': [-23.5505, -46.6333],
            'mexico city': [19.4326, -99.1332],
            'capetown': [-33.9249, 18.4241]
        };

        const key = countryName.toLowerCase();
        return coordinatesMap[key] || null;
    }

    function generateDeliveryTimeline(data) {
        // Define the standard delivery steps
        const deliverySteps = [
            { id: 'order-placed', icon: 'fa-box', label: 'Order Placed', description: 'Shipment created' },
            { id: 'processing', icon: 'fa-cog', label: 'Processing', description: 'Preparing for shipment' },
            { id: 'in-transit', icon: 'fa-truck', label: 'In Transit', description: 'On the way' },
            { id: 'out-for-delivery', icon: 'fa-shipping-fast', label: 'Out for Delivery', description: 'Will be delivered today' },
            { id: 'delivered', icon: 'fa-check-circle', label: 'Delivered', description: 'Package delivered' }
        ];

        // Determine current step based on status
        const status = data.status.toLowerCase();
        let currentStepIndex = 0;

        if (status.includes('delivered')) {
            currentStepIndex = 4;
        } else if (status.includes('out_for_delivery')) {
            currentStepIndex = 3;
        } else if (status.includes('transit')) {
            currentStepIndex = 2;
        } else if (status.includes('processing')) {
            currentStepIndex = 1;
        } else {
            currentStepIndex = 0;
        }

        // Generate HTML for the timeline
        let timelineHTML = '<div class="delivery-steps">';

        // Add each step to the timeline
        deliverySteps.forEach((step, index) => {
            const isCompleted = index <= currentStepIndex;
            const isCurrent = index === currentStepIndex;

            timelineHTML += `
                <div class="delivery-step ${isCompleted ? 'completed' : ''} ${isCurrent ? 'current' : ''}">
                    <div class="step-icon">
                        <i class="fas ${step.icon}"></i>
                    </div>
                    <div class="step-connector ${isCompleted ? 'completed' : ''}"></div>
                    <div class="step-content">
                        <div class="step-label">${step.label}</div>
                        <div class="step-description">${step.description}</div>
                        ${isCurrent ? `<div class="step-status">Current Status</div>` : ''}
                    </div>
                </div>
            `;
        });

        timelineHTML += '</div>';
        return timelineHTML;
    }

    // Sample data for demonstration
    function getSampleTrackingData(trackingNumber) {
        // Check if tracking number exists (in a real app, this would be an API call)
        if (!trackingNumber || trackingNumber.trim() === '') {
            return { error: 'Please enter a tracking number.' };
        }

        // For demo purposes, return sample data
        return {
            tracking_number: trackingNumber,
            status: 'in_transit',
            origin: 'New York, NY',
            destination: 'Los Angeles, CA',
            estimated_delivery: 'June 15, 2023',
            pickup_date: 'June 10, 2023',
            updated_at: 'June 14, 2023 08:00 AM',
            shipper_name: 'John Smith',
            shipper_address: '123 Main St, New York, NY 10001, USA',
            shipper_phone: '(*************',
            shipper_email: '<EMAIL>',
            receiver_name: 'Jane Doe',
            receiver_address: '456 Ocean Ave, Los Angeles, CA 90001, USA',
            receiver_phone: '(*************',
            receiver_email: '<EMAIL>',
            service_type: 'Express',
            weight: '5.2',
            dimensions: '12 x 8 x 6 inches',
            package_type: 'Box',
            items: 'Electronics',
            item_name: 'Smartphone',
            item_description: 'Latest model smartphone with accessories',
            quantity: '1',
            value: '899.99',
            special_instructions: 'Handle with care, fragile electronics',
            events: [
                {
                    date: 'June 10, 2023',
                    time: '09:30 AM',
                    status: 'processing',
                    location: 'New York Sorting Facility'
                },
                {
                    date: 'June 11, 2023',
                    time: '02:45 PM',
                    status: 'in_transit',
                    location: 'Departed New York Hub'
                },
                {
                    date: 'June 13, 2023',
                    time: '10:15 AM',
                    status: 'arrived_at_facility',
                    location: 'Chicago Distribution Center'
                },
                {
                    date: 'June 14, 2023',
                    time: '08:00 AM',
                    status: 'out_for_delivery',
                    location: 'Los Angeles Local Facility'
                },
                {
                    date: 'June 15, 2023',
                    time: '02:30 PM',
                    status: 'delivered',
                    location: 'Los Angeles, CA'
                }
            ]
        };
    }

    // Document Management Functions
    async function loadDocumentRequests(trackingNumber) {
        try {
            const response = await fetch(`/courier/public/api/document-requests.php?tracking_number=${trackingNumber}`);
            const result = await response.json();

            const container = document.getElementById('document-requests-container');
            const documentSection = document.getElementById('document-management-section');

            if (result.success && result.requests.length > 0) {
                // Show the document management section
                documentSection.style.display = 'block';
                container.innerHTML = generateDocumentRequestsHTML(result.requests);
                initializeDocumentUpload();

                // Update sidebar with document information
                updateSidebarDocumentInfo(result.requests);
            } else {
                // Hide the document management section when no requests exist
                documentSection.style.display = 'none';

                // Clear sidebar document information
                updateSidebarDocumentInfo([]);
            }
        } catch (error) {
            console.error('Error loading document requests:', error);

            // Hide section on error and show error in sidebar if needed
            const documentSection = document.getElementById('document-management-section');
            documentSection.style.display = 'none';

            // Could optionally show error in sidebar
            updateSidebarDocumentInfo([]);
        }
    }

    function updateSidebarDocumentInfo(requests) {
        const documentCountBadge = document.getElementById('document-count-badge');
        const documentHistoryContent = document.getElementById('document-history-content');

        if (requests && requests.length > 0) {
            // Show document count badge
            documentCountBadge.textContent = requests.length;
            documentCountBadge.style.display = 'flex';

            // Generate document history HTML
            let historyHTML = '';
            requests.forEach(request => {
                const statusClass = getDocumentStatusClass(request.status);
                historyHTML += `
                    <div class="document-history-item ${request.status}">
                        <div class="document-history-header">
                            <span class="document-history-title">${request.document_type_name}</span>
                            <span class="document-history-status ${statusClass}">${formatDocumentStatus(request.status)}</span>
                        </div>
                        <div class="document-history-date">
                            Requested: ${request.created_at_formatted}
                            ${request.due_date ? `<br>Due: ${request.due_date_formatted}` : ''}
                        </div>
                        ${request.request_message ? `<div class="document-history-notes">${request.request_message}</div>` : ''}
                    </div>
                `;
            });

            documentHistoryContent.innerHTML = historyHTML;
        } else {
            // Hide document count badge
            documentCountBadge.style.display = 'none';

            // Show no content message
            documentHistoryContent.innerHTML = `
                <div class="no-content">
                    <i class="fas fa-file-alt"></i>
                    <p>No document history available</p>
                </div>
            `;
        }
    }

    function generateDocumentRequestsHTML(requests) {
        let html = '<div class="document-requests-list">';

        requests.forEach(request => {
            const statusClass = getDocumentStatusClass(request.status);
            const priorityClass = getPriorityClass(request.priority);
            const isOverdue = request.due_date && new Date(request.due_date) < new Date();

            html += `
                <div class="document-request-item ${statusClass} ${isOverdue ? 'overdue' : ''}" data-request-id="${request.id}">
                    <div class="document-request-header">
                        <div class="document-type">
                            <i class="fas fa-file-alt"></i>
                            <span class="type-name">${request.document_type_name}</span>
                            <span class="priority-badge ${priorityClass}">${request.priority.toUpperCase()}</span>
                        </div>
                        <div class="document-status">
                            <span class="status-badge ${statusClass}">${formatDocumentStatus(request.status)}</span>
                        </div>
                    </div>

                    <div class="document-request-content">
                        ${request.request_message ? `<p class="request-message">${request.request_message}</p>` : ''}

                        ${request.due_date ? `
                            <div class="due-date ${isOverdue ? 'overdue' : ''}">
                                <i class="fas fa-calendar-alt"></i>
                                <span>Due: ${formatDate(request.due_date)}</span>
                                ${isOverdue ? '<span class="overdue-label">OVERDUE</span>' : ''}
                            </div>
                        ` : ''}

                        <div class="document-actions">
                            ${generateDocumentActionsHTML(request)}
                        </div>
                    </div>
                </div>
            `;
        });

        html += '</div>';
        return html;
    }

    function generateDocumentActionsHTML(request) {
        switch (request.status) {
            case 'pending':
                return `
                    <button class="btn btn-primary upload-document-btn" data-request-id="${request.id}">
                        <i class="fas fa-upload"></i>
                        Upload Document
                    </button>
                `;

            case 'uploaded':
                return `
                    <div class="upload-status">
                        <i class="fas fa-check-circle text-success"></i>
                        <span>Document uploaded - awaiting review</span>
                    </div>
                    <button class="btn btn-secondary reupload-document-btn" data-request-id="${request.id}">
                        <i class="fas fa-redo"></i>
                        Replace Document
                    </button>
                `;

            case 'approved':
                return `
                    <div class="approval-status approved">
                        <i class="fas fa-check-circle"></i>
                        <span>Document approved</span>
                    </div>
                `;

            case 'rejected':
                return `
                    <div class="approval-status rejected">
                        <i class="fas fa-times-circle"></i>
                        <span>Document rejected - please reupload</span>
                    </div>
                    <button class="btn btn-primary upload-document-btn" data-request-id="${request.id}">
                        <i class="fas fa-upload"></i>
                        Upload New Document
                    </button>
                `;

            default:
                return '';
        }
    }

    function getDocumentStatusClass(status) {
        const statusClasses = {
            'pending': 'status-pending',
            'uploaded': 'status-uploaded',
            'approved': 'status-approved',
            'rejected': 'status-rejected',
            'cancelled': 'status-cancelled'
        };
        return statusClasses[status] || '';
    }

    function getPriorityClass(priority) {
        const priorityClasses = {
            'low': 'priority-low',
            'medium': 'priority-medium',
            'high': 'priority-high',
            'urgent': 'priority-urgent'
        };
        return priorityClasses[priority] || 'priority-medium';
    }

    function formatDocumentStatus(status) {
        const statusLabels = {
            'pending': 'Pending Upload',
            'uploaded': 'Under Review',
            'approved': 'Approved',
            'rejected': 'Rejected',
            'cancelled': 'Cancelled'
        };
        return statusLabels[status] || status;
    }

    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    function initializeDocumentUpload() {
        // Add event listeners for upload buttons
        document.querySelectorAll('.upload-document-btn, .reupload-document-btn').forEach(button => {
            button.addEventListener('click', function() {
                const requestId = this.getAttribute('data-request-id');
                openDocumentUploadModal(requestId);
            });
        });
    }

    function openDocumentUploadModal(requestId) {
        // Create a simple file upload modal
        const modal = document.createElement('div');
        modal.className = 'document-upload-modal';
        modal.innerHTML = `
            <div class="modal-overlay">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>Upload Document</h3>
                        <button class="close-modal" onclick="this.closest('.document-upload-modal').remove()">×</button>
                    </div>
                    <div class="modal-body">
                        <form id="document-upload-form-${requestId}" enctype="multipart/form-data">
                            <div class="file-upload-area">
                                <input type="file" id="document-file-${requestId}" name="document" accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" required>
                                <label for="document-file-${requestId}">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <span>Choose file or drag and drop</span>
                                    <small>Supported formats: PDF, JPG, PNG, DOC, DOCX (Max 10MB)</small>
                                </label>
                            </div>
                            <div class="upload-notes">
                                <label for="upload-notes-${requestId}">Additional Notes (Optional):</label>
                                <textarea id="upload-notes-${requestId}" name="notes" rows="3" placeholder="Add any additional information about this document..."></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="this.closest('.document-upload-modal').remove()">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="uploadDocument(${requestId})">Upload Document</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    async function uploadDocument(requestId) {
        const form = document.getElementById(`document-upload-form-${requestId}`);
        const fileInput = document.getElementById(`document-file-${requestId}`);
        const notesInput = document.getElementById(`upload-notes-${requestId}`);

        if (!fileInput.files[0]) {
            alert('Please select a file to upload.');
            return;
        }

        const formData = new FormData();
        formData.append('document', fileInput.files[0]);
        formData.append('notes', notesInput.value);
        formData.append('request_id', requestId);

        try {
            const response = await fetch('/courier/public/api/document-upload.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                alert('Document uploaded successfully!');
                document.querySelector('.document-upload-modal').remove();
                // Reload document requests to show updated status
                const trackingNumber = document.getElementById('trackingNumber').value;
                loadDocumentRequests(trackingNumber);
            } else {
                alert('Upload failed: ' + result.message);
            }
        } catch (error) {
            console.error('Upload error:', error);
            alert('Upload failed. Please try again.');
        }
    }

    // Sidebar functionality
    function initializeSidebar() {
        // Add event listeners for sidebar toggles
        document.querySelectorAll('.sidebar-toggle').forEach(toggle => {
            toggle.addEventListener('click', function() {
                const section = this.getAttribute('data-section');
                const panel = document.getElementById(`${section}-panel`);

                // Close all other panels
                document.querySelectorAll('.sidebar-panel').forEach(p => {
                    if (p !== panel) {
                        p.classList.remove('active');
                    }
                });

                // Remove active class from all toggles
                document.querySelectorAll('.sidebar-toggle').forEach(t => {
                    if (t !== this) {
                        t.classList.remove('active');
                    }
                });

                // Toggle current panel
                panel.classList.toggle('active');
                this.classList.toggle('active');
            });
        });

        // Add event listeners for panel close buttons
        document.querySelectorAll('.panel-close').forEach(closeBtn => {
            closeBtn.addEventListener('click', function() {
                const panel = this.closest('.sidebar-panel');
                const section = panel.id.replace('-panel', '');
                const toggle = document.querySelector(`[data-section="${section}"]`);

                panel.classList.remove('active');
                toggle.classList.remove('active');
            });
        });

        // Close panels when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.sidebar-section') && !e.target.closest('.sidebar-panel')) {
                document.querySelectorAll('.sidebar-panel').forEach(panel => {
                    panel.classList.remove('active');
                });
                document.querySelectorAll('.sidebar-toggle').forEach(toggle => {
                    toggle.classList.remove('active');
                });
            }
        });
    }

    async function loadNotifications(trackingNumber) {
        try {
            const response = await fetch(`/courier/public/api/notifications.php?tracking_number=${trackingNumber}`);
            const result = await response.json();

            const notificationCountBadge = document.getElementById('notification-count-badge');
            const notificationsContent = document.getElementById('notifications-content');

            if (result.success && result.notifications.length > 0) {
                const unreadCount = result.notifications.filter(n => !n.is_read).length;

                if (unreadCount > 0) {
                    notificationCountBadge.textContent = unreadCount;
                    notificationCountBadge.style.display = 'flex';
                } else {
                    notificationCountBadge.style.display = 'none';
                }

                // Generate notifications HTML
                let notificationsHTML = '';
                result.notifications.forEach(notification => {
                    notificationsHTML += `
                        <div class="notification-item ${notification.is_read ? 'read' : 'unread'} ${notification.type}"
                             data-notification-id="${notification.id}">
                            <div class="notification-header">
                                <span class="notification-title">${notification.title}</span>
                                <span class="notification-time">${formatDate(notification.created_at)}</span>
                                ${!notification.is_read ? '<span class="notification-unread-indicator"></span>' : ''}
                            </div>
                            <div class="notification-message">${notification.message}</div>
                        </div>
                    `;
                });

                notificationsContent.innerHTML = notificationsHTML;

                // Add click handlers to mark notifications as read
                document.querySelectorAll('.notification-item.unread').forEach(item => {
                    item.addEventListener('click', function() {
                        const notificationId = this.getAttribute('data-notification-id');
                        markNotificationAsRead(notificationId);
                        this.classList.remove('unread');
                        this.classList.add('read');
                        this.querySelector('.notification-unread-indicator')?.remove();

                        // Update badge count
                        const currentCount = parseInt(notificationCountBadge.textContent) || 0;
                        const newCount = Math.max(0, currentCount - 1);
                        if (newCount > 0) {
                            notificationCountBadge.textContent = newCount;
                        } else {
                            notificationCountBadge.style.display = 'none';
                        }
                    });
                });
            } else {
                notificationCountBadge.style.display = 'none';
                notificationsContent.innerHTML = `
                    <div class="no-content">
                        <i class="fas fa-bell"></i>
                        <p>No notifications available</p>
                    </div>
                `;
            }
        } catch (error) {
            console.error('Error loading notifications:', error);
        }
    }

    async function markNotificationAsRead(notificationId) {
        try {
            await fetch('/courier/public/api/mark-notification-read.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ notification_id: notificationId })
            });
        } catch (error) {
            console.error('Error marking notification as read:', error);
        }
    }

    function updateTrackDetailsPanel(data) {
        const trackDetailsContent = document.getElementById('track-details-content');

        if (data) {
            const statusIcon = getStatusIcon(data.status);
            trackDetailsContent.innerHTML = `
                <div class="condensed-track-info">
                    <div class="condensed-status">
                        <div class="condensed-status-icon" style="background: ${getStatusColor(data.status)}">
                            <i class="${statusIcon}"></i>
                        </div>
                        <span class="condensed-status-text">${data.status.replace('_', ' ').toUpperCase()}</span>
                    </div>
                    <div class="condensed-details">
                        <div><strong>Tracking:</strong> ${data.tracking_number}</div>
                        <div><strong>From:</strong> ${data.sender_name || 'N/A'}</div>
                        <div><strong>To:</strong> ${data.receiver_name || 'N/A'}</div>
                        <div><strong>Service:</strong> ${data.service_type || 'Standard'}</div>
                        <div><strong>Last Update:</strong> ${formatDate(data.updated_at)}</div>
                        ${data.current_location ? `<div><strong>Location:</strong> ${data.current_location}</div>` : ''}
                    </div>
                </div>
            `;
        } else {
            trackDetailsContent.innerHTML = `
                <div class="no-content">
                    <i class="fas fa-info-circle"></i>
                    <p>No tracking information available</p>
                </div>
            `;
        }
    }

    function getStatusIcon(status) {
        const icons = {
            'pending': 'fas fa-clock',
            'picked_up': 'fas fa-hand-paper',
            'in_transit': 'fas fa-truck',
            'out_for_delivery': 'fas fa-shipping-fast',
            'delivered': 'fas fa-check-circle',
            'exception': 'fas fa-exclamation-triangle'
        };
        return icons[status] || 'fas fa-info-circle';
    }

    function getStatusColor(status) {
        const colors = {
            'pending': '#FF9800',
            'picked_up': '#2196F3',
            'in_transit': '#9C27B0',
            'out_for_delivery': '#FF5722',
            'delivered': '#4CAF50',
            'exception': '#F44336'
        };
        return colors[status] || '#757575';
    }

    // Initialize tracking when page loads
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize sidebar functionality
        initializeSidebar();

        // Auto-track if tracking number is in URL
        const urlParams = new URLSearchParams(window.location.search);
        const trackingNumber = urlParams.get('tracking_number');

        if (trackingNumber) {
            document.getElementById('trackingNumber').value = trackingNumber;
            trackShipment();
        }
    });
});
